# Email Link Examples for Order Status

## URL Formats for Different Form Types

When sending emails to customers, use these URL formats based on the action needed:

### 1. General Order Status (Default)
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>
```

### 2. New Image Request Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&newImageRequest
```

### 3. Customer Contact Needed Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&customerContactNeeded
```

### 4. Customer Approval Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&customerApproval
```

### 5. Direct Order Number in Path (any form type)
```
https://cors-dev.cuddleclones.com/order-status/124453?newImageRequest
https://cors-dev.cuddleclones.com/order-status/124453?customerContactNeeded
https://cors-dev.cuddleclones.com/order-status/124453?customerApproval
```

## Form-Specific Features

### New Image Request Form
**When to use:** When an image is rejected and customer needs to upload a new one
**Features:**
- Shows the rejected image with rejection reason
- Allows customer to upload a new image
- Checkbox for "I don't have this" option
- Free-form text field for additional information
- Image level processing

### Customer Contact Needed Form
**When to use:** When artwork team needs customer input on a specific image
**Features:**
- Shows the selected reference image
- Displays message from the artwork team
- Allows customer to upload a new image (optional)
- Free-form text field for customer response
- Item level processing

### Customer Approval Form
**When to use:** When artwork is ready for customer approval
**Features:**
- Shows the artwork/image for approval
- "Approve" button → Changes status to "Ready for Vendor"
- "Request Revision" button → Opens revision dialog
- Revision dialog includes:
  - Free-form text field: "What Revision Are You Requesting?"
  - Submit button → Changes status to "Revision Requested"
  - Cancel button → Closes dialog
- Logs requests with timestamps and submitter info
- Adds text to item notes in Order Detail page

## How It Works

### Server-Side Rendering (SSR) with Security
1. Customer clicks the email link
2. Next.js server receives the request
3. Server extracts `shopifyOrderNumber`, `customerEmail`, and form type from URL parameters
4. Server calls the backend API: `POST /orders/public/status` with:
   - Order number and customer email from URL
   - Customer validation token (base64 encoded) from environment variable
   - Form type for appropriate UI rendering
5. Backend validates the customer token and verifies order ownership
6. Server renders the page with the order data and appropriate form already loaded
7. Customer sees the order status and form immediately (no loading spinner)

### Security Implementation
- **Customer Validation Token**: Base64 encoded token stored in environment variables
- **Server-Side Validation**: Backend validates token before returning order data
- **Customer Email Verification**: Order must belong to the provided customer email
- **POST Request**: Uses POST instead of GET for better security with token in body

### URL Parameter Extraction
The page component automatically extracts:
- `shopifyOrderNumber` from `?shopifyOrderNumber=124453`
- `customerEmail` from `&customerEmail=<EMAIL>`
- Form type from `&newImageRequest`, `&customerContactNeeded`, or `&customerApproval`

### Error Handling
If the order is not found or there's an error:
- Invalid token → "Invalid access. Please use the link from your email."
- Order not found → "Order not found. Please check your order number and try again."
- Server error → "Server error. Please try again later."
- All errors are handled server-side with appropriate user messages

## Email Template Example

```html
<p>Dear {{customerFirstName}},</p>
<p>Your order #{{shopifyOrderNumber}} status has been updated.</p>
<p><a href="https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber={{shopifyOrderNumber}}&customerEmail={{customerEmail}}">
   Click here to view your order status
</a></p>
```

## Benefits

1. **Fast Loading**: Order data is fetched server-side
2. **SEO Friendly**: Content is rendered on the server
3. **No Authentication Required**: Public access via URL parameters
4. **Better UX**: No loading spinners for customers
5. **Email Tracking**: Can track which customers clicked the links

## Security Implementation

### Environment Variables Required

#### Backend (.env)
```bash
CUSTOMER_VALIDATION_TOKEN=your-secret-customer-validation-token-here
```

#### Frontend (.env)
```bash
NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN=eW91ci1zZWNyZXQtY3VzdG9tZXItdmFsaWRhdGlvbi10b2tlbi1oZXJl
```

**Note**: The frontend token should be the base64 encoded version of the backend token.

### Token Generation Example
```bash
# Generate base64 token
echo -n "your-secret-customer-validation-token-here" | base64
# Output: eW91ci1zZWNyZXQtY3VzdG9tZXItdmFsaWRhdGlvbi10b2tlbi1oZXJl
```

### Security Features
- **Token Validation**: All requests require valid customer validation token
- **Customer Email Verification**: Order must belong to the provided customer email
- **Base64 Encoding**: Token is base64 encoded for transmission
- **Server-Side Validation**: All validation happens on the backend
- **No Sensitive Data**: Only public order information is exposed
- **POST Requests**: Uses POST instead of GET for better security

### API Endpoint Security
```typescript
// Request Body
{
  "shopifyOrderNumber": "124453",
  "customerEmail": "<EMAIL>",
  "customerValidationToken": "eW91ci1zZWNyZXQtY3VzdG9tZXItdmFsaWRhdGlvbi10b2tlbi1oZXJl",
  "formType": "newImageRequest" // optional
}

// Response (success)
{
  "orderNumber": "124453",
  "orderDate": "2024-01-15T10:30:00Z",
  "orderStatus": "unfulfilled",
  "customerFirstName": "John",
  "customerLastName": "Doe",
  "itemCount": 2,
  "lineItems": [...]
}

// Response (error)
{
  "statusCode": 401,
  "message": "Invalid customer validation token"
}
```
