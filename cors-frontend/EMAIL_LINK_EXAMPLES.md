# Email Link Examples for Order Status

## Correct URL Format

When sending emails to customers, use these URL formats:

### Option 1: Query Parameters (Recommended)
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>
```

### Option 2: Direct Order Number in Path
```
https://cors-dev.cuddleclones.com/order-status/124453
```

## How It Works

### Server-Side Rendering (SSR)
1. Customer clicks the email link
2. Next.js server receives the request
3. Server extracts `shopifyOrderNumber` and `customerEmail` from URL parameters
4. Server calls the backend API: `GET /orders/public/status/124453`
5. Server renders the page with the order data already loaded
6. Customer sees the order status immediately (no loading spinner)

### URL Parameter Extraction
The page component automatically extracts:
- `shopifyOrderNumber` from `?shopifyOrderNumber=124453`
- `customerEmail` from `&customerEmail=<EMAIL>`

### Error Handling
If the order is not found or there's an error:
- The error is handled server-side
- Customer sees an appropriate error message
- No additional client-side API calls needed

## Email Template Example

```html
<p>Dear {{customerFirstName}},</p>
<p>Your order #{{shopifyOrderNumber}} status has been updated.</p>
<p><a href="https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber={{shopifyOrderNumber}}&customerEmail={{customerEmail}}">
   Click here to view your order status
</a></p>
```

## Benefits

1. **Fast Loading**: Order data is fetched server-side
2. **SEO Friendly**: Content is rendered on the server
3. **No Authentication Required**: Public access via URL parameters
4. **Better UX**: No loading spinners for customers
5. **Email Tracking**: Can track which customers clicked the links

## Security Notes

- Only public order information is exposed
- No sensitive customer data in the API response
- Order lookup is limited to order number only
- Customer email can be used for additional validation if needed
