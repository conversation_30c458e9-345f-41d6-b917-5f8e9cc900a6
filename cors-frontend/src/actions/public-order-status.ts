'use server'

import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;


export interface PublicOrderStatusResponse {
  shopifyOrderNumber: string;
  orderDate: string;
  orderStatus: string;
  statusUpdatedAt: string;
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  itemCount: number;
  shippingAddress?: {
    city: string;
    country: string;
    address1: string;
    address2?: string | null;
    province: string;
    country_code: string;
    province_code: string;
  };
  lineItems: Array<{
    id: string;
    itemNumber: string;
    priority: string;
    quantity: number;
    status: string;
    currentStatus?: string;
    lastUpdatedAt?: string;
    rejectedImage?: {
      url: string;
      rejectionReason?: string;
    };
    selectedImage?: {
      url: string;
      message?: string;
    };
  }>;
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
  formType?: string,
): Promise<PublicOrderStatusResponse> {
  try {
    // Return dummy data for testing UI/UX
    console.log('Using dummy data for order:', orderNumber, 'formType:', formType);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const dummyData: PublicOrderStatusResponse = {
      shopifyOrderNumber: orderNumber,
      orderDate: '2024-01-15T10:30:00Z',
      statusUpdatedAt: '2024-01-20T14:45:00Z',
      orderStatus: 'unfulfilled',
      customerFirstName: 'John',
      customerLastName: 'Doe',
      customerEmail: customerEmail || '<EMAIL>',
      itemCount: 3,
      shippingAddress: {
        city: 'North New Hyde Park',
        country: 'United States',
        address1: '1999 Marcus Avenue',
        address2: 'Suite 200',
        province: 'New York',
        country_code: 'US',
        province_code: 'NY'
      },
      lineItems: [
        {
          id: 'line-item-1',
          itemNumber: 'ITEM-001',
          priority: 'High',
          quantity: 1,
          status: 'pending',
          currentStatus: 'pending',
          lastUpdatedAt: '2024-01-20T14:45:00Z',
          rejectedImage: formType === 'newImageRequest' ? {
            url: 'https://dev-shopify-custom-integration.oss-accelerate.aliyuncs.com/print_images/7bc6e997-581c-4d34-af0d-11107cdab673.jpeg',
            rejectionReason: 'Image quality is too low. Please provide a higher resolution image with better lighting.'
          } : undefined,
          selectedImage: formType === 'customerContactNeeded' ? {
            url: 'https://via.placeholder.com/400x300/4ecdc4/ffffff?text=Reference+Image',
            message: 'We need clarification about the design placement. Please review the attached reference and let us know your preferences.'
          } : formType === 'customerApproval' ? {
            url: 'https://via.placeholder.com/400x300/45b7d1/ffffff?text=Artwork+Preview',
            message: 'Please review this artwork preview and approve or request revisions.'
          } : undefined
        },
        {
          id: 'line-item-2',
          itemNumber: 'ITEM-002',
          priority: 'Medium',
          quantity: 2,
          status: 'in_progress',
          currentStatus: 'in_progress',
          lastUpdatedAt: '2024-01-19T09:30:00Z'
        },
        {
          id: 'line-item-3',
          itemNumber: 'ITEM-003',
          priority: 'Normal',
          quantity: 1,
          status: 'completed',
          currentStatus: 'completed',
          lastUpdatedAt: '2024-01-18T16:20:00Z'
        }
      ]
    };

    return dummyData;

    // TODO: Uncomment when backend API is ready
    
    // const publicApiClient = axios.create({
    //   baseURL: API_URL,
    //   withCredentials: false,
    //   headers: {
    //     Authorization: `Bearer ${accessToken}`,
    //   },
    // });

    // const requestBody = {
    //   orderNumber: orderNumber,
    //   email: customerEmail || '',
    //   formType: formType || undefined,
    // };

    // const response = await publicApiClient.post('/order-tracking', requestBody);
    // console.log('Response:', response.data);

    // if (!response.data) {
    //   throw new Error('No data returned from server');
    // }

    // return response.data;
    
  } catch (error) {
    console.error('Error fetching public order status:', error);

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        throw new Error('Order not found. Please check your order number and try again.');
      }
      if (error.response?.status === 401) {
        throw new Error('Invalid access. Please use the link from your shopify order tracking.');
      }
      if (error.response?.status && error.response.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }
      throw new Error(error.response?.data?.message || 'Failed to fetch order status');
    }

    throw new Error('Failed to fetch order status');
  }
}

export async function requestNewImage(orderNumber: string, lineItemId: string, reason: string): Promise<void> {
  try {
    // This would be implemented when the backend endpoint is ready
    // For now, we'll just log the request
    console.log('Request new image:', { orderNumber, lineItemId, reason });
    
    // TODO: Implement actual API call when backend endpoint is available
    // const publicApiClient = axios.create({
    //   baseURL: API_URL,
    //   withCredentials: false,
    // });
    
    // await publicApiClient.post(`/orders/public/request-new-image`, {
    //   orderNumber,
    //   lineItemId,
    //   reason
    // });
    
    throw new Error('Request new image functionality is not yet implemented');
  } catch (error) {
    console.error('Error requesting new image:', error);
    throw error;
  }
}
