import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusWithNumberPageProps {
  params: { orderNumber: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

const OrderStatusWithNumberPage: React.FC<OrderStatusWithNumberPageProps> = async ({
  params,
  searchParams,
}) => {
  let orderData: PublicOrderStatus | null = null;
  let error: string | null = null;

  // Extract customer email and form type from search params
  const customerEmail =
    typeof searchParams.customerEmail === 'string' ? searchParams.customerEmail : undefined;

  // Determine form type based on URL parameters
  let formType: string | undefined;
  if (searchParams.newImageRequest !== undefined) {
    formType = 'newImageRequest';
  } else if (searchParams.customerContactNeeded !== undefined) {
    formType = 'customerContactNeeded';
  } else if (searchParams.customerApproval !== undefined) {
    formType = 'customerApproval';
  }

  // Fetch data server-side using the order number from URL
  try {
    orderData = await fetchPublicOrderStatus(params.orderNumber, customerEmail, formType);
  } catch (err) {
    error = err instanceof Error ? err.message : 'Failed to fetch order status';
  }

  return (
    <PublicOrderStatusPage
      initialOrderData={orderData}
      initialError={error}
      shopifyOrderNumber={params.orderNumber}
      customerEmail={customerEmail}
      formType={
        formType as 'newImageRequest' | 'customerContactNeeded' | 'customerApproval' | undefined
      }
    />
  );
};

export default OrderStatusWithNumberPage;
