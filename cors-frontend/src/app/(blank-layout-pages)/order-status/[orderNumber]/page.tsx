import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusWithNumberPageProps {
  params: { orderNumber: string };
}

const OrderStatusWithNumberPage: React.FC<OrderStatusWithNumberPageProps> = ({ params }) => {
  return <PublicOrderStatusPage initialOrderNumber={params.orderNumber} />;
};

export default OrderStatusWithNumberPage;
