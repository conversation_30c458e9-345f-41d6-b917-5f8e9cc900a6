import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

const OrderStatusPage: React.FC<OrderStatusPageProps> = async ({ searchParams }) => {
  // Extract parameters from URL
  const shopifyOrderNumber =
    typeof searchParams.shopifyOrderNumber === 'string'
      ? searchParams.shopifyOrderNumber
      : undefined;

  const customerEmail =
    typeof searchParams.customerEmail === 'string' ? searchParams.customerEmail : undefined;

  let orderData: PublicOrderStatus | null = null;
  let error: string | null = null;

  // If we have order number, fetch data server-side
  if (shopifyOrderNumber) {
    try {
      orderData = await fetchPublicOrderStatus(shopifyOrderNumber);
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to fetch order status';
    }
  }

  return (
    <PublicOrderStatusPage
      initialOrderData={orderData}
      initialError={error}
      shopifyOrderNumber={shopifyOrderNumber}
      customerEmail={customerEmail}
    />
  );
};

export default OrderStatusPage;
