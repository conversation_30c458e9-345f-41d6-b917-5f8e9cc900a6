import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

const OrderStatusPage: React.FC<OrderStatusPageProps> = ({ searchParams }) => {
  // Extract order number from search params if provided
  const orderNumber = typeof searchParams.orderNumber === 'string' 
    ? searchParams.orderNumber 
    : undefined;

  return <PublicOrderStatusPage initialOrderNumber={orderNumber} />;
};

export default OrderStatusPage;
