'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON>ton,
  TextField,
  Box,
  Typography,
  Card,
  CardMedia,
  Alert,
} from '@mui/material';
import { toast } from 'react-toastify';
import { PublicLineItem, CustomerApprovalForm } from '@/types/public-order-status.types';

interface CustomerApprovalFormProps {
  open: boolean;
  onClose: () => void;
  lineItem: PublicLineItem | null;
  isApproval: boolean; // true for approval, false for revision request
  onSubmit: (formData: CustomerApprovalForm) => Promise<void>;
}

const CustomerApprovalFormComponent: React.FC<CustomerApprovalFormProps> = ({
  open,
  onClose,
  lineItem,
  isApproval,
  onSubmit,
}) => {
  const [revisionText, setRevisionText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!isApproval && !revisionText.trim()) {
      toast.error('Please describe what revision you are requesting');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        lineItemId: lineItem?.id || '',
        approved: isApproval,
        revisionText: isApproval ? undefined : revisionText.trim(),
      });

      if (isApproval) {
        toast.success('Item approved successfully! Status changed to "Ready for Vendor"');
      } else {
        toast.success(
          'Revision request submitted successfully! Status changed to "Revision Requested"',
        );
      }
      onClose();
    } catch (error) {
      toast.error(`Failed to ${isApproval ? 'approve' : 'request revision for'} item`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setRevisionText('');
    onClose();
  };

  if (!lineItem) return null;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isApproval ? 'Approve Item' : 'Request Revision'} - Item #
        {lineItem.itemNumber || lineItem.id.slice(-8)}
      </DialogTitle>

      <DialogContent>
        {/* <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Product SKU: {lineItem.productSku?.sku}
          </Typography>
        </Box> */}

        {/* Show artwork/image for approval */}
        {lineItem.selectedImage && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Artwork for {isApproval ? 'Approval' : 'Revision'}
            </Typography>
            <Card sx={{ maxWidth: 400, mb: 2 }}>
              <CardMedia
                component="img"
                height="300"
                image={lineItem.selectedImage.url}
                alt="Artwork for approval"
              />
            </Card>
          </Box>
        )}

        {isApproval ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="body1">
              <strong>Approve this item?</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Clicking "Approve" will change the status to "Ready for Vendor" and the item will
              proceed to production.
            </Typography>
          </Alert>
        ) : (
          <Box>
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body1">
                <strong>Request a Revision</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Please describe what changes you would like to see. This will change the status to
                "Revision Requested" and notify our artwork team.
              </Typography>
            </Alert>

            <TextField
              fullWidth
              multiline
              rows={4}
              label="What Revision Are You Requesting?"
              placeholder="Please describe the specific changes you would like to see..."
              value={revisionText}
              onChange={e => setRevisionText(e.target.value)}
              required
              sx={{ mb: 2 }}
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color={isApproval ? 'success' : 'warning'}
          disabled={isSubmitting || (!isApproval && !revisionText.trim())}
        >
          {isSubmitting
            ? isApproval
              ? 'Approving...'
              : 'Submitting...'
            : isApproval
              ? 'Approve'
              : 'Submit Revision Request'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerApprovalFormComponent;
