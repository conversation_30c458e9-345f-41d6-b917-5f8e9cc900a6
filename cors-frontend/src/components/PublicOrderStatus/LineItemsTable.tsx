'use client';
import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
  Avatar,
} from '@mui/material';
import { Inventory2, Flag, CheckCircle } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import { PublicLineItem, FormType } from '@/types/public-order-status.types';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
  formType?: FormType;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber, formType }) => {
  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({lineItems.length})
            </Typography>
          </Box>

          <TableContainer
            component={Paper}
            variant="outlined"
            sx={{
              borderRadius: 2,
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Inventory2 fontSize="small" />
                      Item Details
                    </Box>
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                      <Flag fontSize="small" />
                      Priority
                    </Box>
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    Quantity
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <CheckCircle fontSize="small" />
                      Status
                    </Box>
                  </TableCell>
                  {formType && (
                    <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                      Actions
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map((item, index) => (
                  <TableRow
                    key={item.id}
                    hover
                    sx={{
                      '&:nth-of-type(odd)': { bgcolor: 'grey.25' },
                      '&:hover': { bgcolor: 'primary.50' },
                    }}
                  >
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            bgcolor: 'primary.main',
                            fontSize: '0.875rem',
                          }}
                        >
                          {index + 1}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            Item #{item.itemNumber || item.id.slice(-8)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {item.id.slice(-8)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={item.priority || 'Normal'}
                        size="small"
                        color={
                          item.priority === 'High'
                            ? 'error'
                            : item.priority === 'Medium'
                              ? 'warning'
                              : 'default'
                        }
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2" fontWeight={500}>
                        {item.quantity}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={item.currentStatus || item.status}
                        variant="lineItem"
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </>
  );
};

export default LineItemsTable;
