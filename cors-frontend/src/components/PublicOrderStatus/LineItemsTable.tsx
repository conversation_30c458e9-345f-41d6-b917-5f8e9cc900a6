'use client';
import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
  Avatar,
} from '@mui/material';
import { Inventory2, Flag, CheckCircle } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import { PublicLineItem, FormType } from '@/types/public-order-status.types';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
  formType?: FormType;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber, formType }) => {
  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({lineItems.length})
            </Typography>
          </Box>

          <TableContainer
            component={Paper}
            variant="outlined"
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              '& .MuiTableHead-root': {
                bgcolor: 'primary.50',
              },
              '& .MuiTableHead-root .MuiTableCell-root': {
                fontWeight: 'bold',
                color: 'primary.main',
                borderBottom: '2px solid',
                borderBottomColor: 'primary.main',
              },
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Inventory2 fontSize="small" />
                      Item Details
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                      <Flag fontSize="small" />
                      Priority
                    </Box>
                  </TableCell>
                  <TableCell align="center">Quantity</TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <CheckCircle fontSize="small" />
                      Current Status
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map((item, index) => (
                  <TableRow
                    key={item.id}
                    sx={{
                      '&:nth-of-type(odd)': {
                        bgcolor: 'grey.50',
                      },
                      '&:hover': {
                        bgcolor: 'primary.50',
                        transform: 'scale(1.01)',
                        transition: 'all 0.2s ease-in-out',
                      },
                      transition: 'all 0.2s ease-in-out',
                    }}
                  >
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          sx={{
                            width: 48,
                            height: 48,
                            bgcolor:
                              item.priority === 'High'
                                ? 'error.main'
                                : item.priority === 'Medium'
                                  ? 'warning.main'
                                  : 'primary.main',
                            fontSize: '1rem',
                            fontWeight: 'bold',
                            boxShadow: 2,
                          }}
                        >
                          {index + 1}
                        </Avatar>
                        <Box>
                          <Typography variant="body1" fontWeight={700} color="text.primary">
                            Item #{item.itemNumber || item.id.slice(-8)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {item.id.slice(-8)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={item.priority || 'Normal'}
                        size="medium"
                        color={
                          item.priority === 'High'
                            ? 'error'
                            : item.priority === 'Medium'
                              ? 'warning'
                              : 'success'
                        }
                        variant="filled"
                        sx={{
                          fontWeight: 'bold',
                          minWidth: 80,
                          boxShadow: 1,
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          bgcolor: 'info.light',
                          color: 'info.contrastText',
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                        }}
                      >
                        {item.quantity}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={item.currentStatus || item.status}
                        variant="lineItem"
                        size="medium"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </>
  );
};

export default LineItemsTable;
