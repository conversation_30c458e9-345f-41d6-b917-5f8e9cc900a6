'use client';
import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
  Avatar,
} from '@mui/material';
import { Inventory2, Flag, CheckCircle } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import { PublicLineItem, FormType } from '@/types/public-order-status.types';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
  formType?: FormType;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber, formType }) => {
  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({lineItems.length})
            </Typography>
          </Box>

          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              border: '1px solid',
              // borderColor: 'grey.200',
              '& .MuiTableHead-root': {
                bgcolor: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
              },
              '& .MuiTableHead-root .MuiTableCell-root': {
                fontWeight: 600,
                color: 'text.primary',
                borderBottom: 'none',
                py: 2,
                fontSize: '0.875rem',
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
              },
            }}
          >
            <Table sx={{ '& .MuiTableCell-root': { borderBottom: 'none' } }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: '40%' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Inventory2 fontSize="small" color="primary" />
                      Item Details
                    </Box>
                  </TableCell>
                  <TableCell align="center" sx={{ width: '20%' }}>
                    <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                      <Flag fontSize="small" color="warning" />
                      Priority
                    </Box>
                  </TableCell>
                  <TableCell align="center" sx={{ width: '15%' }}>
                    Quantity
                  </TableCell>
                  <TableCell sx={{ width: '25%' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <CheckCircle fontSize="small" color="success" />
                      Status
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map((item, index) => (
                  <TableRow
                    key={item.id}
                    sx={{
                      '&:nth-of-type(odd)': {
                        // bgcolor: 'grey.50',
                      },
                      '&:hover': {
                        bgcolor: 'primary.50',
                        transform: 'scale(1.01)',
                        transition: 'all 0.2s ease-in-out',
                      },
                      transition: 'all 0.2s ease-in-out',
                    }}
                  >
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          sx={{
                            width: 48,
                            height: 48,
                            bgcolor:
                              item.priority === 'High'
                                ? 'error.main'
                                : item.priority === 'Medium'
                                  ? 'warning.main'
                                  : 'primary.main',
                            fontSize: '1rem',
                            fontWeight: 'bold',
                            boxShadow: 2,
                          }}
                        >
                          {index + 1}
                        </Avatar>
                        <Box>
                          <Typography variant="body1" fontWeight={700} color="text.primary">
                            Item #{item.itemNumber || item.id.slice(-8)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {item.id.slice(-8)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={item.priority || 'Normal'}
                        size="small"
                        color={
                          item.priority === 'High'
                            ? 'error'
                            : item.priority === 'Medium'
                              ? 'warning'
                              : 'success'
                        }
                        variant="filled"
                        sx={{
                          fontWeight: 'bold',
                          minWidth: 70,
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          bgcolor: 'info.light',
                          color: 'info.contrastText',
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                        }}
                      >
                        {item.quantity}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={item.currentStatus || item.status}
                        variant="lineItem"
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </>
  );
};

export default LineItemsTable;
