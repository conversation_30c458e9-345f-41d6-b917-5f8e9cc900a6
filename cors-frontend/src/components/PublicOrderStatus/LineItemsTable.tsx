'use client'

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Alert
} from '@mui/material';
import { toast } from 'react-toastify';
import PublicStatusChip from './PublicStatusChip';
import { PublicLineItem } from '@/types/public-order-status.types';
import { requestNewImage } from '@/actions/public-order-status';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber }) => {
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [selectedLineItem, setSelectedLineItem] = useState<PublicLineItem | null>(null);
  const [requestReason, setRequestReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRequestNewImage = (lineItem: PublicLineItem) => {
    setSelectedLineItem(lineItem);
    setRequestDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setRequestDialogOpen(false);
    setSelectedLineItem(null);
    setRequestReason('');
  };

  const handleSubmitRequest = async () => {
    if (!selectedLineItem || !requestReason.trim()) {
      toast.error('Please provide a reason for the request');
      return;
    }

    setIsSubmitting(true);
    try {
      await requestNewImage(orderNumber, selectedLineItem.id, requestReason.trim());
      toast.success('Your request has been submitted successfully');
      handleCloseDialog();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items ({lineItems.length})
          </Typography>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Item #</TableCell>
                  <TableCell>Product SKU</TableCell>
                  <TableCell align="center">Quantity</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map((item) => (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {item.itemNumber || item.id.slice(-8)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {item.productSku?.sku || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">
                        {item.quantity}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip 
                        status={item.status} 
                        variant="lineItem" 
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        onClick={() => handleRequestNewImage(item)}
                        disabled={item.status === 'cancelled'}
                      >
                        Request New Image
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Request New Image Dialog */}
      <Dialog 
        open={requestDialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Request New Image
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Item: {selectedLineItem?.itemNumber || selectedLineItem?.id.slice(-8)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              SKU: {selectedLineItem?.productSku?.sku}
            </Typography>
          </Box>
          
          <TextField
            autoFocus
            margin="dense"
            label="Reason for Request"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={requestReason}
            onChange={(e) => setRequestReason(e.target.value)}
            placeholder="Please describe why you need a new image for this item..."
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmitRequest} 
            variant="contained"
            disabled={isSubmitting || !requestReason.trim()}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LineItemsTable;
