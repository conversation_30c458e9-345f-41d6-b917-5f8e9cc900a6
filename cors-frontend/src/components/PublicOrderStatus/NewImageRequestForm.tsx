'use client';

import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  <PERSON>Field,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  Card,
  CardMedia,
  Alert,
  Paper,
  Avatar,
  Chip,
  IconButton,
  LinearProgress,
} from '@mui/material';
import {
  CloudUpload,
  Error,
  CheckCircle,
  Close,
  Image as ImageIcon,
  Warning,
  Info,
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { PublicLineItem, NewImageRequestForm } from '@/types/public-order-status.types';

interface NewImageRequestFormProps {
  open: boolean;
  onClose: () => void;
  lineItem: PublicLineItem | null;
  onSubmit: (formData: NewImageRequestForm) => Promise<void>;
}

const NewImageRequestFormComponent: React.FC<NewImageRequestFormProps> = ({
  open,
  onClose,
  lineItem,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<NewImageRequestForm>({
    lineItemId: lineItem?.id || '',
    dontHaveImage: false,
    customerText: '',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragOver, setDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }
      setSelectedFile(file);
      setFormData(prev => ({ ...prev, newImage: file }));
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileChange({ target: { files: [file] } } as any);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const handleSubmit = async () => {
    if (!formData.dontHaveImage && !selectedFile) {
      toast.error('Please upload an image or check "I don\'t have this"');
      return;
    }

    if (!formData.customerText.trim()) {
      toast.error('Please provide additional information');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        lineItemId: lineItem?.id || '',
        newImage: selectedFile || undefined,
      });
      toast.success('Request submitted successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      lineItemId: '',
      dontHaveImage: false,
      customerText: '',
    });
    setSelectedFile(null);
    onClose();
  };

  if (!lineItem) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      {/* Custom Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
          color: 'white',
          p: 3,
          position: 'relative',
        }}
      >
        <IconButton
          onClick={handleClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
          }}
        >
          <Close />
        </IconButton>

        <Box display="flex" alignItems="center" gap={2}>
          <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
            <ImageIcon fontSize="large" />
          </Avatar>
          <Box>
            <Typography variant="h5" fontWeight="bold">
              New Image Request
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              Item #{lineItem.itemNumber || lineItem.id.slice(-8)}
            </Typography>
          </Box>
        </Box>
      </Box>

      <DialogContent sx={{ p: 0 }}>
        {isSubmitting && <LinearProgress />}

        <Box sx={{ p: 3 }}>
          {/* Item Information */}
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              mb: 3,
              borderRadius: 2,
            }}
          >
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <Info color="primary" fontSize="small" />
              <Typography variant="subtitle2" fontWeight="bold">
                Item Information
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Priority: <Chip label={lineItem.priority || 'Normal'} size="small" />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Quantity: {lineItem.quantity}
            </Typography>
          </Paper>

          {/* Show rejected image if available */}
          {lineItem.rejectedImage && (
            <Box sx={{ mb: 3 }}>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <Error color="error" />
                <Typography variant="h6" fontWeight="bold" color="error">
                  Rejected Image
                </Typography>
              </Box>

              <Card
                sx={{
                  maxWidth: 400,
                  mb: 2,
                  borderRadius: 2,
                  overflow: 'hidden',
                }}
              >
                <CardMedia
                  component="img"
                  height="250"
                  image={lineItem.rejectedImage.url}
                  alt="Rejected image"
                  sx={{ objectFit: 'cover' }}
                />
              </Card>

              {/* {lineItem.rejectedImage.rejectionReason && (
                <Alert
                  severity="error"
                  sx={{
                    mb: 2,
                    borderRadius: 2,
                    '& .MuiAlert-message': {
                      width: '100%',
                    },
                  }}
                >
                  <Typography variant="body2">
                    <strong>Rejection Reason:</strong>
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    {lineItem.rejectedImage.rejectionReason}
                  </Typography>
                </Alert>
              )} */}
            </Box>
          )}

          {/* Upload new image section */}
          <Box sx={{ mb: 3 }}>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <CloudUpload color="primary" />
              <Typography variant="h6" fontWeight="bold">
                Upload New Image
              </Typography>
            </Box>

            {/* <FormControlLabel
              control={
                <Checkbox
                  checked={formData.dontHaveImage}
                  onChange={e =>
                    setFormData(prev => ({
                      ...prev,
                      dontHaveImage: e.target.checked,
                    }))
                  }
                />
              }
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <Warning color="warning" fontSize="small" />
                  <Typography variant="body2">I don't have this image available</Typography>
                </Box>
              }
              sx={{ mb: 2 }}
            /> */}

            {!formData.dontHaveImage && (
              <Paper
                variant="outlined"
                sx={{
                  p: 3,
                  textAlign: 'center',
                  borderStyle: 'dashed',
                  borderWidth: 2,
                  borderColor: dragOver ? 'primary.main' : 'grey.300',
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: 'primary.50',
                  },
                }}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <input
                  id="file-input"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  style={{ display: 'none' }}
                />

                {selectedFile ? (
                  <Box>
                    <CheckCircle color="success" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" color="success.main" gutterBottom>
                      File Selected
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedFile.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                    <Typography variant="h6" gutterBottom>
                      Drop your image here
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      or click to browse files
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Supports: JPG, PNG, GIF (Max 10MB)
                    </Typography>
                  </Box>
                )}
              </Paper>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting} size="large">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={
            isSubmitting ||
            (!formData.dontHaveImage && !selectedFile) ||
            !formData.customerText.trim()
          }
          size="large"
          sx={{
            minWidth: 140,
            borderRadius: 2,
          }}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Request'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NewImageRequestFormComponent;
