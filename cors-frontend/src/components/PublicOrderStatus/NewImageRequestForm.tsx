'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  Card,
  CardMedia,
  Alert,
  Input,
} from '@mui/material';
import { toast } from 'react-toastify';
import { PublicLineItem, NewImageRequestForm } from '@/types/public-order-status.types';

interface NewImageRequestFormProps {
  open: boolean;
  onClose: () => void;
  lineItem: PublicLineItem | null;
  onSubmit: (formData: NewImageRequestForm) => Promise<void>;
}

const NewImageRequestFormComponent: React.FC<NewImageRequestFormProps> = ({
  open,
  onClose,
  lineItem,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<NewImageRequestForm>({
    lineItemId: lineItem?.id || '',
    dontHaveImage: false,
    customerText: '',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData(prev => ({ ...prev, newImage: file }));
    }
  };

  const handleSubmit = async () => {
    if (!formData.dontHaveImage && !selectedFile) {
      toast.error('Please upload an image or check "I don\'t have this"');
      return;
    }

    if (!formData.customerText.trim()) {
      toast.error('Please provide additional information');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        lineItemId: lineItem?.id || '',
        newImage: selectedFile || undefined,
      });
      toast.success('Request submitted successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      lineItemId: '',
      dontHaveImage: false,
      customerText: '',
    });
    setSelectedFile(null);
    onClose();
  };

  if (!lineItem) return null;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        New Image Request - Item #{lineItem.itemNumber || lineItem.id.slice(-8)}
      </DialogTitle>

      <DialogContent>
        {/* Show rejected image if available */}
        {lineItem.rejectedImage && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom color="error">
              Rejected Image
            </Typography>
            <Card sx={{ maxWidth: 300, mb: 2 }}>
              <CardMedia
                component="img"
                height="200"
                image={lineItem.rejectedImage.url}
                alt="Rejected image"
              />
            </Card>
            {lineItem.rejectedImage.rejectionReason && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Rejection Reason:</strong> {lineItem.rejectedImage.rejectionReason}
                </Typography>
              </Alert>
            )}
          </Box>
        )}

        {/* Upload new image section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Upload New Image
          </Typography>

          <FormControlLabel
            control={
              <Checkbox
                checked={formData.dontHaveImage}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    dontHaveImage: e.target.checked,
                  }))
                }
              />
            }
            label="I don't have this"
            sx={{ mb: 2 }}
          />

          {!formData.dontHaveImage && (
            <Box>
              <Input
                type="file"
                inputProps={{ accept: 'image/*' }}
                onChange={handleFileChange}
                sx={{ mb: 2 }}
              />
              {selectedFile && (
                <Typography variant="body2" color="success.main">
                  Selected: {selectedFile.name}
                </Typography>
              )}
            </Box>
          )}
        </Box>

        {/* Customer text field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Additional Information"
            placeholder="Please provide any additional details or instructions..."
            value={formData.customerText}
            onChange={e =>
              setFormData(prev => ({
                ...prev,
                customerText: e.target.value,
              }))
            }
            required
          />
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} variant="contained" disabled={isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Submit Request'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NewImageRequestFormComponent;
