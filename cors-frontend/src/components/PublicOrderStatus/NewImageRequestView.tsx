'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typo<PERSON>,
  Box,
  Button,
  Alert,
  Paper,
  Avatar,
  CardMedia,
  TextField,
  FormControlLabel,
  Checkbox,
  LinearProgress,
} from '@mui/material';
import { Inventory, Error, CloudUpload, Warning, Delete } from '@mui/icons-material';
import { toast } from 'react-toastify';
import PublicStatusChip from './PublicStatusChip';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface NewImageRequestViewProps {
  orderData: PublicOrderStatus;
}

const NewImageRequestView: React.FC<NewImageRequestViewProps> = ({ orderData }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [dontHaveImage, setDontHaveImage] = useState(false);
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragOver, setDragOver] = useState(false);

  // Get the first line item with rejected image
  const rejectedItem = orderData.lineItems.find(item => item.rejectedImage);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }

      setSelectedFile(file);

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileChange({ target: { files: [file] } } as any);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const handleDeleteImage = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const handleSubmit = async () => {
    if (!dontHaveImage && !selectedFile) {
      toast.error('Please upload an image or check "I don\'t have this image"');
      return;
    }

    if (!additionalInfo.trim()) {
      toast.error('Please provide additional information');
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Your image request has been submitted successfully!');

      // Reset form
      handleDeleteImage();
      setDontHaveImage(false);
      setAdditionalInfo('');
    } catch (error) {
      toast.error('Failed to submit request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header Card */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <CardContent sx={{ py: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              <Inventory fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold">
                New Image Request
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                Order #{orderData.shopifyOrderNumber}
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Order Status:
            </Typography>
            <PublicStatusChip status={orderData.orderStatus} variant="order" size="small" />

            {rejectedItem && (
              <>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Item #{rejectedItem.itemNumber}:
                </Typography>
                <PublicStatusChip status={rejectedItem.status} variant="lineItem" size="small" />
              </>
            )}
          </Box>
        </CardContent>
      </Card>

      {isSubmitting && <LinearProgress sx={{ mb: 2 }} />}

      {/* Rejected Image Section */}
      {rejectedItem?.rejectedImage && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Error color="error" />
              <Typography variant="h6" fontWeight="bold" color="error">
                Rejected Image
              </Typography>
            </Box>

            <Card
              sx={{
                maxWidth: 400,
                mb: 2,
                borderRadius: 2,
                overflow: 'hidden',
              }}
            >
              <CardMedia
                component="img"
                height="250"
                image={rejectedItem.rejectedImage.url}
                alt="Rejected image"
                sx={{ objectFit: 'cover' }}
              />
            </Card>
          </CardContent>
        </Card>
      )}

      {/* Upload New Image Section */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <CloudUpload color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Upload New Image
            </Typography>
          </Box>

          {!dontHaveImage && (
            <>
              {/* Image Preview */}
              {previewUrl && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Image Preview:
                  </Typography>
                  <Box position="relative" display="inline-block">
                    <Card sx={{ maxWidth: 300, borderRadius: 2 }}>
                      <CardMedia
                        component="img"
                        height="200"
                        image={previewUrl}
                        alt="Preview"
                        sx={{ objectFit: 'cover' }}
                      />
                    </Card>
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      startIcon={<Delete />}
                      onClick={handleDeleteImage}
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        minWidth: 'auto',
                        borderRadius: 2,
                      }}
                    >
                      Delete
                    </Button>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {selectedFile?.name} ({((selectedFile?.size || 0) / 1024 / 1024).toFixed(2)} MB)
                  </Typography>
                </Box>
              )}

              {/* Upload Zone */}
              {!previewUrl && (
                <Paper
                  variant="outlined"
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    borderStyle: 'dashed',
                    borderWidth: 2,
                    borderColor: dragOver ? 'primary.main' : 'grey.300',
                    borderRadius: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'primary.50',
                    },
                  }}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onClick={() => document.getElementById('file-input')?.click()}
                >
                  <input
                    id="file-input"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                  />

                  <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                  <Typography variant="h6" gutterBottom>
                    Drop your image here
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    or click to browse files
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Supports: JPG, PNG, GIF (Max 10MB)
                  </Typography>
                </Paper>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Box textAlign="center">
        <Button
          variant="contained"
          size="large"
          onClick={handleSubmit}
          disabled={isSubmitting || (!dontHaveImage && !selectedFile) || !additionalInfo.trim()}
          sx={{
            minWidth: 200,
            py: 1.5,
            borderRadius: 2,
            fontSize: '1.1rem',
          }}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Request'}
        </Button>
      </Box>
    </Box>
  );
};

export default NewImageRequestView;
