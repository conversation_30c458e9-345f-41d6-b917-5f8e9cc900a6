'use client';

import React from 'react';
import { Card, CardContent, Typography, Box, Divider } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { format } from 'date-fns';
import PublicStatusChip from './PublicStatusChip';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface OrderDetailsCardProps {
  orderData: PublicOrderStatus;
}

const OrderDetailsCard: React.FC<OrderDetailsCardProps> = ({ orderData }) => {
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Card elevation={2} sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom color="primary">
          Order #{orderData?.shopifyOrderNumber}
        </Typography>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={3}>
          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Order Date
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {formatDate(orderData.orderDate)}
              </Typography>
            </Box>
          </Grid>

          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Status
              </Typography>
              <PublicStatusChip status={orderData.orderStatus} variant="order" size="medium" />
            </Box>
          </Grid>

          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Customer
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {orderData.customerFirstName} {orderData.customerLastName}
              </Typography>
            </Box>
          </Grid>

          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Email
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {orderData.customerEmail}
              </Typography>
            </Box>
          </Grid>

          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Items
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {orderData.itemCount} {orderData.itemCount === 1 ? 'item' : 'items'}
              </Typography>
            </Box>
          </Grid>

          <Grid sx={{ xs: 12, sm: 6, md: 3 }}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Last Status UpdatedAt
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {formatDate(orderData.statusUpdatedAt)}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default OrderDetailsCard;
