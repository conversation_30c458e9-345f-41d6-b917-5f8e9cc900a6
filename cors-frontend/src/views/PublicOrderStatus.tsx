'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Paper,
  Button,
  TextField,
  InputAdornment,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { toast } from 'react-toastify';
import { OrderDetailsCard, LineItemsTable } from '@/components/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface PublicOrderStatusPageProps {
  initialOrderNumber?: string;
}

const PublicOrderStatusPage: React.FC<PublicOrderStatusPageProps> = ({ initialOrderNumber }) => {
  const [orderNumber, setOrderNumber] = useState(initialOrderNumber || '');
  const [orderData, setOrderData] = useState<PublicOrderStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);

  // Auto-search if initial order number is provided
  useEffect(() => {
    if (initialOrderNumber) {
      handleSearch();
    }
  }, [initialOrderNumber]);

  const handleSearch = async () => {
    if (!orderNumber.trim()) {
      toast.error('Please enter an order number');
      return;
    }

    setLoading(true);
    setError(null);
    setHasSearched(true);

    try {
      const data = await fetchPublicOrderStatus(orderNumber.trim());
      setOrderData(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch order status';
      setError(errorMessage);
      setOrderData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleReset = () => {
    setOrderNumber('');
    setOrderData(null);
    setError(null);
    setHasSearched(false);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          Order Status
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Track your order progress and request updates
        </Typography>
      </Box>

      {/* Loading State */}
      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" py={4}>
          <CircularProgress size={40} />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Searching for your order...
          </Typography>
        </Box>
      )}

      {/* Error State */}
      {error && !loading && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Order Data */}
      {orderData && !loading && (
        <Box>
          <OrderDetailsCard orderData={orderData} />
          <LineItemsTable lineItems={orderData.lineItems} orderNumber={orderData.orderNumber} />
        </Box>
      )}

      {/* No Results State */}
      {hasSearched && !orderData && !loading && !error && (
        <Alert severity="info">
          No order found with the provided order number. Please check your order number and try
          again.
        </Alert>
      )}

      {/* Help Section */}
      <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Need Help?
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          If you're having trouble with your order or need assistance, please contact our customer
          support team.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • Make sure you're prvoiding the correct order information
        </Typography>
      </Paper>
    </Container>
  );
};

export default PublicOrderStatusPage;
